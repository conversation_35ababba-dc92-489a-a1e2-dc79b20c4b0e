notification:
  slack:
    enable: true
    url: *********************************************************************************

spring:
  cloud.stream:
    kafka:
      binder:
        brokers: "localhost:9092"
        replicationFactor: 1
        minPartitionCount: 1
        configuration:
          security:
            protocol: PLAINTEXT

  tc-platform-jwt:
    public-key-url: http://127.0.0.1:8082/oauth2/jwks
    authorize-ignored:
      paths: "*"
    ignored-public-key-on-load: true
otp:
  salt: "testsalt"

otp-config:
  default-config:
    time-gap: 20
server:
  port: 9086

logging:
  level:
    com.tyme.gotyme.otp: DEBUG
    org.hibernate.SQL: DEBUG
    org.springframework.data.redis: TRACE
  config: classpath:log4j2-patternLayout.xml

otp.feature:
  gtph-legacy-apis: true
