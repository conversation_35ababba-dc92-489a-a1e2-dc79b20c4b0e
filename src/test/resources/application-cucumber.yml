spring:
  profiles:
    active: cucumber
  
  # JPA Configuration for tests
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQLDialect
  
  # Disable Flyway for tests
  flyway:
    enabled: false
  
  # Kafka configuration will be set by TestContainers
  cloud:
    stream:
      kafka:
        binder:
          autoCreateTopics: true
          autoAddPartitions: true
        bindings:
          notificationProducer-out-0:
            destination: notification-topic
            content-type: application/json
          smsConsumer-in-0:
            destination: notification-topic
            content-type: application/json
            group: test-consumer-group

# OTP Configuration for tests
otp-config:
  salt: "test-salt"
  active-country: "gtph"
  default-config:
    time-gap: 30
    timeout: 300
    max-attempt: 3
    max-resend-attempt: 2
    digit-length: 6
    reference-id-digits: 4
    short-request-limit: 1
    zone-id: "Asia/Manila"
    originator: "GoTyme"
  entities-config:
    gtph:
      test-entity:
        time-gap: 30
        timeout: 300
        max-attempt: 3
        max-resend-attempt: 2
        digit-length: 6
        reference-id-digits: 4
        short-request-limit: 1
        zone-id: "Asia/Manila"
        originator: "GoTyme"

# OTP Bypass Configuration for tests
otp-bypass:
  events: "LOGIN_OTP,REGISTRATION_OTP"
  identifier:
    PROFILE: "test-bypass-profile"
    EMAIL: "<EMAIL>"
    PHONE: "+639999999999"

# Feature flags for tests
otp:
  feature:
    gtph-legacy-apis: true
    sim-swap-check: false

# JWT Configuration - disable for tests
tc-platform-jwt:
  authorize-ignored:
    paths: "/**"
  ignored-public-key-on-load: true

# Logging configuration
logging:
  level:
    com.tyme.gotyme.otp: DEBUG
    org.springframework.kafka: INFO
    org.testcontainers: INFO
    io.cucumber: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
