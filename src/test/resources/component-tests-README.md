# Component Tests for OTP Resend Functionality

This document describes the component test framework for testing OTP resend functionality using Cucumber BDD, TestContainers, and other testing technologies.

## Technologies Used

### 1. Cucumber (v7.20.1)
- **Purpose**: Behavior-Driven Development (BDD) framework
- **Features**: 
  - Write test scenarios in Gherkin language
  - Easy to understand business requirements
  - Integration with Spring through `@CucumberContextConfiguration`

### 2. TestContainers
- **Purpose**: Provides Docker containers for integration testing
- **Containers Used**:
  - **MySQL**: Test database
  - **Kafka**: Message broker for notifications
  - **LocalStack**: Mock AWS services (SQS, CloudFormation)
  - **Redis**: Caching layer

### 3. Spring Boot Test
- **Annotations**:
  - `@SpringBootTest`: Starts full Spring application context
  - `@DynamicPropertySource`: Dynamic configuration from containers
  - `@ActiveProfiles("cucumber")`: Activates cucumber test profile

### 4. WireMock
- **Purpose**: Mock external HTTP services
- **Configuration**: Runs on port 9561
- **Mocked Services**:
  - Step-up authentication service
  - Fraud provider service
  - WSO2 API Gateway

### 5. ECR Image Substitution
- **Purpose**: Avoid Docker Hub rate limiting
- **Implementation**: `ECRImageNameSubstitutor` replaces Docker Hub images with Amazon ECR equivalents

## Test Structure

### Configuration Classes
- `CucumberSpringConfiguration`: Main Cucumber configuration with Spring Boot
- `TestContainersConfiguration`: Sets up and configures all test containers
- `WireMockConfig`: Configures WireMock for external service mocking
- `ECRImageNameSubstitutor`: Handles Docker image substitution

### Test Helpers
- `ScenarioState`: Manages state between Cucumber steps
- `FileHelper`: Utilities for reading test data files
- `KafkaProducer`: Sends test messages to Kafka topics
- `ConsumeSmsMessage`: Consumes SMS notification messages from Kafka
- `EventProducer`: Creates and sends various test events

### Step Definitions
- `OtpResendSteps`: Main step definitions for OTP resend scenarios
- `CommonSteps`: Common setup and cleanup steps

### Feature Files
- `otp-resend.feature`: Contains all OTP resend test scenarios

## Running the Tests

### Prerequisites
- Java 21
- Docker (for TestContainers)
- Gradle

### Commands

#### Run all component tests:
```bash
./gradlew cucumber
```

#### Run specific scenarios by tags:
```bash
./gradlew cucumber -Dcucumber.filter.tags="@smoke"
./gradlew cucumber -Dcucumber.filter.tags="@error-handling"
./gradlew cucumber -Dcucumber.filter.tags="not @ignore"
```

#### Generate HTML reports:
```bash
./gradlew cucumber cluecumberReports
```

## Test Scenarios

### 1. Basic Resend Functionality (@smoke)
- Successfully resend OTP within allowed limits
- Multiple resends within limit
- Verify new reference IDs are generated
- Verify previous OTPs are invalidated

### 2. Error Handling (@error-handling)
- Reject resend when max attempts reached
- Handle various error conditions

### 3. Time Gap Validation (@time-gap)
- Respect time gap configuration between requests
- Handle rapid successive requests

### 4. Verification (@verification)
- Verify OTP after resend
- Ensure latest OTP works correctly

### 5. Authorization (@unauthorized)
- Test resend for unauthorized users
- Verify proper access controls

### 6. Edge Cases (@edge-cases)
- Handle expired sessions
- Handle various boundary conditions

### 7. Performance (@performance)
- Multiple users resending simultaneously
- Load testing scenarios

## Test Data

### Files Location: `src/test/resources/testdata/`
- `step-up-validation-response.json`: Mock response for step-up validation

### WireMock Stubs Location: `src/test/resources/wiremock/`
- `mappings/`: Request mapping definitions
- `__files/`: Response body templates

## Configuration

### Test Profile: `application-cucumber.yml`
- Database configuration for TestContainers
- Kafka configuration
- OTP service configuration
- Feature flags for testing
- Logging configuration

### Key Configuration Properties:
```yaml
otp-config:
  default-config:
    max-resend-attempt: 2  # Maximum resend attempts
    time-gap: 30          # Seconds between requests
    timeout: 300          # OTP expiration time
```

## Debugging

### View Test Reports:
- Cucumber HTML Report: `build/reports/cucumber/index.html`
- Cluecumber Report: `build/reports/cucumber-html-reports/index.html`
- JSON Report: `build/reports/cucumber-report.json`

### Logs:
- Application logs show detailed OTP operations
- TestContainers logs show container startup
- Kafka logs show message flow

### Common Issues:
1. **Container startup failures**: Check Docker daemon is running
2. **Port conflicts**: Ensure ports 9561 (WireMock) are available
3. **Kafka connection issues**: Check TestContainers Kafka setup
4. **Database issues**: Verify MySQL container is healthy

## Extending Tests

### Adding New Scenarios:
1. Add scenario to `otp-resend.feature`
2. Implement step definitions in `OtpResendSteps`
3. Add test data files if needed
4. Update WireMock stubs if mocking new services

### Adding New Test Helpers:
1. Create helper class in `helpers/` package
2. Annotate with `@Component`
3. Inject into step definitions

### Adding New Mock Services:
1. Add WireMock mappings in `wiremock/mappings/`
2. Add response files in `wiremock/__files/`
3. Configure in `WireMockConfig`

## Best Practices

1. **Use meaningful scenario names** that describe business value
2. **Keep step definitions focused** on single responsibilities
3. **Use ScenarioState** to share data between steps
4. **Clean up after each scenario** in CommonSteps
5. **Use appropriate tags** for organizing test execution
6. **Mock external dependencies** consistently
7. **Verify both positive and negative scenarios**
8. **Include performance and edge case testing**
