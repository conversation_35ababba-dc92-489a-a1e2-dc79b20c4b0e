Feature: OTP Resend Functionality
  As a user
  I want to be able to resend <PERSON><PERSON> when needed
  So that I can complete my authentication even if I don't receive the first OTP

  Background:
    Given the step-up authentication service is available
    And the fraud provider service allows the request

  @smoke
  Scenario: Successfully resend <PERSON><PERSON> within allowed limits
    Given a user with profile ID "test-profile-123" and cellphone "+639171234567"
    When the user requests an OTP for the first time
    Then the OTP should be generated successfully
    And the response should contain a reference ID
    And the response should contain a masked cellphone
    And an SMS notification should be sent
    
    When the user requests to resend the OTP
    Then the OTP should be generated successfully
    And a new reference ID should be generated
    And the previous OTP should be invalidated
    And an SMS notification should be sent

  @smoke
  Scenario: Successfully resend <PERSON>TP multiple times within limit
    Given a user with profile ID "test-profile-456" and cellphone "+639171234568"
    When the user requests an OTP for the first time
    Then the OTP should be generated successfully
    
    When the user requests to resend the OTP
    Then the OTP should be generated successfully
    And a new reference ID should be generated
    
    When the user requests to resend the OTP again
    Then the OTP should be generated successfully
    And a new reference ID should be generated

  @error-handling
  Scenario: Reject resend request when max attempts reached
    Given a user with profile ID "test-profile-789" and cellphone "+639171234569"
    When the user requests an OTP for the first time
    Then the OTP should be generated successfully
    
    When the user requests to resend the OTP
    Then the OTP should be generated successfully
    
    When the user requests to resend the OTP again
    Then the OTP should be generated successfully
    
    When the user requests to resend the OTP for the 4 time
    Then the request should be rejected with max resend attempts error

  @time-gap
  Scenario: Resend OTP respects time gap configuration
    Given a user with profile ID "test-profile-101" and cellphone "+639171234570"
    When the user requests an OTP for the first time
    Then the OTP should be generated successfully
    
    And the time gap between requests is less than 30 seconds
    When the user requests to resend the OTP
    Then the OTP should be generated successfully

  @verification
  Scenario: Verify OTP after resend
    Given a user with profile ID "test-profile-202" and cellphone "+639171234571"
    When the user requests an OTP for the first time
    Then the OTP should be generated successfully
    
    When the user requests to resend the OTP
    Then the OTP should be generated successfully
    And a new reference ID should be generated
    
    When the user verifies the OTP with the latest reference ID
    Then the OTP verification should be successful

  @unauthorized
  Scenario: Resend OTP for unauthorized user
    Given a user with profile ID "test-profile-303" and cellphone "+639171234572"
    When the user requests an OTP for the first time
    Then the OTP should be generated successfully
    
    When the user requests to resend the OTP
    Then the OTP should be generated successfully
    And a new reference ID should be generated

  @edge-cases
  Scenario: Resend OTP with expired session
    Given a user with profile ID "test-profile-404" and cellphone "+639171234573"
    When the user requests an OTP for the first time
    Then the OTP should be generated successfully
    
    And I wait for 301 seconds
    When the user requests to resend the OTP
    Then the OTP should be generated successfully

  @performance
  Scenario: Multiple users resending OTP simultaneously
    Given a user with profile ID "test-profile-501" and cellphone "+639171234574"
    When the user requests an OTP for the first time
    Then the OTP should be generated successfully
    
    When the user requests to resend the OTP
    Then the OTP should be generated successfully
    And a new reference ID should be generated
