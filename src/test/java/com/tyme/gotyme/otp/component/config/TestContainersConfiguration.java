package com.tyme.gotyme.otp.component.config;

import com.redis.testcontainers.RedisContainer;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.containers.localstack.LocalStackContainer.Service;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

@Testcontainers
public class TestContainersConfiguration {

    private static final ECRImageNameSubstitutor imageSubstitutor = new ECRImageNameSubstitutor();

    @Container
    static final MySQLContainer<?> mysql = new MySQLContainer<>(
        imageSubstitutor.apply(DockerImageName.parse("mysql:8.0"))
    )
        .withDatabaseName("otp_test_db")
        .withUsername("test")
        .withPassword("test")
        .withReuse(true);

    @Container
    static final KafkaContainer kafka = new KafkaContainer(
        imageSubstitutor.apply(DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
    )
        .withReuse(true);

    @Container
    static final LocalStackContainer localstack = new LocalStackContainer(
        imageSubstitutor.apply(DockerImageName.parse("localstack/localstack:3.0"))
    )
        .withServices(Service.SQS, Service.CLOUDFORMATION)
        .withReuse(true);

    @Container
    static final RedisContainer redis = new RedisContainer(
        imageSubstitutor.apply(DockerImageName.parse("redis:7.0-alpine"))
    )
        .withReuse(true);

    static {
        // Start all containers
        mysql.start();
        kafka.start();
        localstack.start();
        redis.start();
    }

    public static void configureProperties(DynamicPropertyRegistry registry) {
        // MySQL configuration
        registry.add("spring.datasource.url", mysql::getJdbcUrl);
        registry.add("spring.datasource.username", mysql::getUsername);
        registry.add("spring.datasource.password", mysql::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "com.mysql.cj.jdbc.Driver");
        
        // JPA configuration
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
        registry.add("spring.jpa.show-sql", () -> "true");
        
        // Flyway configuration
        registry.add("spring.flyway.enabled", () -> "false");
        
        // Kafka configuration
        registry.add("spring.cloud.stream.kafka.binder.brokers", kafka::getBootstrapServers);
        registry.add("spring.cloud.stream.kafka.binder.replicationFactor", () -> "1");
        registry.add("spring.cloud.stream.kafka.binder.minPartitionCount", () -> "1");
        registry.add("spring.cloud.stream.kafka.binder.autoCreateTopics", () -> "true");
        registry.add("spring.cloud.stream.kafka.binder.autoAddPartitions", () -> "true");
        registry.add("spring.cloud.stream.kafka.binder.configuration.security.protocol", () -> "PLAINTEXT");
        
        // LocalStack configuration
        registry.add("cloud.aws.sqs.endpoint", () -> localstack.getEndpointOverride(Service.SQS).toString());
        registry.add("cloud.aws.region.static", () -> localstack.getRegion());
        registry.add("cloud.aws.credentials.access-key", () -> localstack.getAccessKey());
        registry.add("cloud.aws.credentials.secret-key", () -> localstack.getSecretKey());
        
        // Redis configuration
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", () -> redis.getMappedPort(6379).toString());
        
        // Test-specific configurations
        registry.add("otp-config.salt", () -> "test-salt");
        registry.add("otp-config.active-country", () -> "gtph");
        registry.add("otp-config.default-config.time-gap", () -> "30");
        registry.add("otp-config.default-config.timeout", () -> "300");
        registry.add("otp-config.default-config.max-attempt", () -> "3");
        registry.add("otp-config.default-config.max-resend-attempt", () -> "2");
        registry.add("otp-config.default-config.digit-length", () -> "6");
        registry.add("otp-config.default-config.zone-id", () -> "Asia/Manila");
        registry.add("otp-config.default-config.originator", () -> "GoTyme");
        
        // Disable JWT validation for tests
        registry.add("tc-platform-jwt.authorize-ignored.paths", () -> "/**");
        registry.add("tc-platform-jwt.ignored-public-key-on-load", () -> "true");
    }

    public static MySQLContainer<?> getMysql() {
        return mysql;
    }

    public static KafkaContainer getKafka() {
        return kafka;
    }

    public static LocalStackContainer getLocalstack() {
        return localstack;
    }

    public static RedisContainer getRedis() {
        return redis;
    }
}
