package com.tyme.gotyme.otp.component.config;

import io.cucumber.spring.CucumberContextConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

@CucumberContextConfiguration
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("cucumber")
public class CucumberSpringConfiguration {

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Configure dynamic properties from TestContainers
        TestContainersConfiguration.configureProperties(registry);
        WireMockConfig.configureProperties(registry);
    }
}
