package com.tyme.gotyme.otp.component.helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * File helper for reading test data files
 */
@Component
public class FileHelper {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Read file content as string from classpath
     */
    public String readFileAsString(String filePath) {
        try {
            ClassPathResource resource = new ClassPathResource(filePath);
            return Files.readString(resource.getFile().toPath(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException("Failed to read file: " + filePath, e);
        }
    }

    /**
     * Read JSON file and parse as JsonNode
     */
    public JsonNode readJsonFile(String filePath) {
        try {
            String content = readFileAsString(filePath);
            return objectMapper.readTree(content);
        } catch (IOException e) {
            throw new RuntimeException("Failed to parse JSON file: " + filePath, e);
        }
    }

    /**
     * Read JSON file and parse as specific type
     */
    public <T> T readJsonFile(String filePath, Class<T> valueType) {
        try {
            String content = readFileAsString(filePath);
            return objectMapper.readValue(content, valueType);
        } catch (IOException e) {
            throw new RuntimeException("Failed to parse JSON file: " + filePath, e);
        }
    }

    /**
     * Write content to file in test output directory
     */
    public void writeToFile(String fileName, String content) {
        try {
            Path outputDir = Paths.get("build/test-output");
            Files.createDirectories(outputDir);
            Path filePath = outputDir.resolve(fileName);
            Files.write(filePath, content.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            throw new RuntimeException("Failed to write file: " + fileName, e);
        }
    }

    /**
     * Replace placeholders in template file with actual values
     */
    public String processTemplate(String templatePath, String placeholder, String value) {
        String template = readFileAsString(templatePath);
        return template.replace("{{" + placeholder + "}}", value);
    }
}
