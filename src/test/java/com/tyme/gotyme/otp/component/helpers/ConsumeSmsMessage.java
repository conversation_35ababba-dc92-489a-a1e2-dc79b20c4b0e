package com.tyme.gotyme.otp.component.helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.stereotype.Component;
import org.testcontainers.containers.KafkaContainer;

import java.time.Duration;
import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * Kafka consumer helper for consuming SMS notification messages
 */
@Component
public class ConsumeSmsMessage {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private Consumer<String, String> consumer;

    public ConsumeSmsMessage() {
        initializeConsumer();
    }

    private void initializeConsumer() {
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, getKafkaBootstrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "test-consumer-group");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 1000);

        this.consumer = new org.apache.kafka.clients.consumer.KafkaConsumer<>(props);
    }

    private String getKafkaBootstrapServers() {
        KafkaContainer kafka = com.tyme.gotyme.otp.component.config.TestContainersConfiguration.getKafka();
        return kafka.getBootstrapServers();
    }

    /**
     * Subscribe to SMS notification topic and wait for message
     */
    public JsonNode waitForSmsMessage(String topic, long timeoutSeconds) {
        try {
            consumer.subscribe(Collections.singletonList(topic));
            
            long startTime = System.currentTimeMillis();
            long timeoutMillis = TimeUnit.SECONDS.toMillis(timeoutSeconds);
            
            while (System.currentTimeMillis() - startTime < timeoutMillis) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                
                for (ConsumerRecord<String, String> record : records) {
                    String messageValue = record.value();
                    JsonNode messageJson = objectMapper.readTree(messageValue);
                    
                    // Check if this is an SMS notification message
                    if (isSmsMessage(messageJson)) {
                        return messageJson;
                    }
                }
            }
            
            throw new RuntimeException("No SMS message received within " + timeoutSeconds + " seconds");
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to consume SMS message from topic: " + topic, e);
        }
    }

    /**
     * Wait for SMS message with specific reference ID
     */
    public JsonNode waitForSmsMessageWithReferenceId(String topic, String referenceId, long timeoutSeconds) {
        try {
            consumer.subscribe(Collections.singletonList(topic));
            
            long startTime = System.currentTimeMillis();
            long timeoutMillis = TimeUnit.SECONDS.toMillis(timeoutSeconds);
            
            while (System.currentTimeMillis() - startTime < timeoutMillis) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                
                for (ConsumerRecord<String, String> record : records) {
                    String messageValue = record.value();
                    JsonNode messageJson = objectMapper.readTree(messageValue);
                    
                    // Check if this is an SMS message with the expected reference ID
                    if (isSmsMessage(messageJson) && hasReferenceId(messageJson, referenceId)) {
                        return messageJson;
                    }
                }
            }
            
            throw new RuntimeException("No SMS message with reference ID " + referenceId + 
                " received within " + timeoutSeconds + " seconds");
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to consume SMS message from topic: " + topic, e);
        }
    }

    /**
     * Check if the message is an SMS notification
     */
    private boolean isSmsMessage(JsonNode message) {
        JsonNode channel = message.path("channel");
        return channel.isTextual() && "SMS".equalsIgnoreCase(channel.asText());
    }

    /**
     * Check if the message contains the expected reference ID
     */
    private boolean hasReferenceId(JsonNode message, String expectedReferenceId) {
        JsonNode notiData = message.path("notiData");
        JsonNode referenceId = notiData.path("referenceId");
        return referenceId.isTextual() && expectedReferenceId.equals(referenceId.asText());
    }

    /**
     * Extract OTP code from SMS message
     */
    public String extractOtpCode(JsonNode smsMessage) {
        JsonNode notiData = smsMessage.path("notiData");
        JsonNode otpCode = notiData.path("otpCode");
        return otpCode.isTextual() ? otpCode.asText() : null;
    }

    /**
     * Extract reference ID from SMS message
     */
    public String extractReferenceId(JsonNode smsMessage) {
        JsonNode notiData = smsMessage.path("notiData");
        JsonNode referenceId = notiData.path("referenceId");
        return referenceId.isTextual() ? referenceId.asText() : null;
    }

    /**
     * Close the consumer
     */
    public void close() {
        if (consumer != null) {
            consumer.close();
        }
    }
}
