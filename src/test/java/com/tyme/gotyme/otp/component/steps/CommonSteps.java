package com.tyme.gotyme.otp.component.steps;

import com.tyme.gotyme.otp.component.config.WireMockConfig;
import com.tyme.gotyme.otp.component.helpers.ScenarioState;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Common step definitions for setup and cleanup
 */
public class CommonSteps {

    @Autowired
    private ScenarioState scenarioState;

    @Before
    public void setUp() {
        // Clear scenario state before each scenario
        scenarioState.clear();

        // Reset WireMock stubs
        WireMockConfig.resetWireMock();

        // Initialize default values
        scenarioState.put(ScenarioState.GENERATED_OTP_COUNT, 0);
    }

    @After
    public void tearDown() {
        // Clean up after each scenario
        scenarioState.clear();

        // Reset WireMock
        WireMockConfig.resetWireMock();
    }
}
