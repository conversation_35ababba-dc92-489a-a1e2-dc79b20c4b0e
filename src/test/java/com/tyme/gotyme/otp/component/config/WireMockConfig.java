package com.tyme.gotyme.otp.component.config;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;

/**
 * WireMock configuration for mocking external HTTP services
 */
public class WireMockConfig {

    private static final int WIREMOCK_PORT = 9561;
    private static WireMockServer wireMockServer;

    static {
        wireMockServer = new WireMockServer(
            WireMockConfiguration.options()
                .port(WIREMOCK_PORT)
                .usingFilesUnderDirectory("src/test/resources/wiremock")
        );
        wireMockServer.start();
    }

    public static void configureProperties(DynamicPropertyRegistry registry) {
        // Configure external service URLs to point to WireMock
        String wireMockBaseUrl = "http://localhost:" + WIREMOCK_PORT;
        
        // Step-up authentication service
        registry.add("step-up-auth.base-url", () -> wireMockBaseUrl);
        
        // AML integration service
        registry.add("aml-integration.base-url", () -> wireMockBaseUrl);
        
        // WSO2 API Gateway
        registry.add("wso2.api-gateway.auth-url", () -> wireMockBaseUrl + "/oauth2/token");
        registry.add("wso2.api-gateway.public-key-url", () -> wireMockBaseUrl + "/oauth2/jwks");
    }

    public static WireMockServer getWireMockServer() {
        return wireMockServer;
    }

    public static void resetWireMock() {
        wireMockServer.resetAll();
    }

    public static void stopWireMock() {
        if (wireMockServer != null && wireMockServer.isRunning()) {
            wireMockServer.stop();
        }
    }
}
