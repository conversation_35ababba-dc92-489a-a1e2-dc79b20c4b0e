package com.tyme.gotyme.otp.component.helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.testcontainers.containers.KafkaContainer;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * Kafka producer helper for sending test messages
 */
@Component
public class KafkaProducer {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private Producer<String, String> producer;

    @Autowired
    public KafkaProducer() {
        initializeProducer();
    }

    private void initializeProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, getKafkaBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 0);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);

        this.producer = new org.apache.kafka.clients.producer.KafkaProducer<>(props);
    }

    private String getKafkaBootstrapServers() {
        // Get Kafka bootstrap servers from TestContainers
        KafkaContainer kafka = com.tyme.gotyme.otp.component.config.TestContainersConfiguration.getKafka();
        return kafka.getBootstrapServers();
    }

    /**
     * Send a message to Kafka topic
     */
    public void sendMessage(String topic, String key, Object message) {
        try {
            String messageJson = objectMapper.writeValueAsString(message);
            ProducerRecord<String, String> record = new ProducerRecord<>(topic, key, messageJson);
            
            producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    throw new RuntimeException("Failed to send message to Kafka", exception);
                }
            }).get(10, TimeUnit.SECONDS);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to send message to Kafka topic: " + topic, e);
        }
    }

    /**
     * Send a simple string message to Kafka topic
     */
    public void sendStringMessage(String topic, String key, String message) {
        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(topic, key, message);
            
            producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    throw new RuntimeException("Failed to send message to Kafka", exception);
                }
            }).get(10, TimeUnit.SECONDS);
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to send message to Kafka topic: " + topic, e);
        }
    }

    /**
     * Close the producer
     */
    public void close() {
        if (producer != null) {
            producer.close();
        }
    }
}
