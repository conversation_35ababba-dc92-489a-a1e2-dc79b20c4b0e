package com.tyme.gotyme.otp.component.config;

import org.testcontainers.utility.DockerImageName;

import java.util.function.UnaryOperator;

/**
 * ECR Image Name Substitutor to avoid Docker Hub rate limiting
 * by using Amazon ECR Public Gallery images instead
 */
public class ECRImageNameSubstitutor implements UnaryOperator<DockerImageName> {

    private static final String ECR_PUBLIC_REGISTRY = "public.ecr.aws";

    @Override
    public DockerImageName apply(DockerImageName original) {
        String originalName = original.asCanonicalNameString();

        // Map common Docker Hub images to ECR equivalents
        if (originalName.startsWith("mysql:")) {
            return DockerImageName.parse(ECR_PUBLIC_REGISTRY + "/docker/library/mysql:" + original.getVersionPart())
                .asCompatibleSubstituteFor("mysql");
        }

        if (originalName.startsWith("confluentinc/cp-kafka:")) {
            return DockerImageName.parse(ECR_PUBLIC_REGISTRY + "/confluentinc/cp-kafka:" + original.getVersionPart())
                .asCompatibleSubstituteFor("confluentinc/cp-kafka");
        }

        if (originalName.startsWith("localstack/localstack:")) {
            return DockerImageName.parse(ECR_PUBLIC_REGISTRY + "/localstack/localstack:" + original.getVersionPart())
                .asCompatibleSubstituteFor("localstack/localstack");
        }

        if (originalName.startsWith("redis:")) {
            return DockerImageName.parse(ECR_PUBLIC_REGISTRY + "/docker/library/redis:" + original.getVersionPart())
                .asCompatibleSubstituteFor("redis");
        }

        // If no mapping found, return original
        return original;
    }
}
