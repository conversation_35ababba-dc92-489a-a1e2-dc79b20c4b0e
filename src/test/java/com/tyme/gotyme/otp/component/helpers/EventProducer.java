package com.tyme.gotyme.otp.component.helpers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Event producer helper for creating and sending test events
 */
@Component
public class EventProducer {

    @Autowired
    private KafkaProducer kafkaProducer;

    /**
     * Create and send a step-up authentication event
     */
    public String createStepUpAuthEvent(String profileId, String cellphone, String channel) {
        String stepUpAuthId = UUID.randomUUID().toString();
        
        Map<String, Object> event = new HashMap<>();
        event.put("stepUpAuthId", stepUpAuthId);
        event.put("profileId", profileId);
        event.put("cellphone", cellphone);
        event.put("channel", channel);
        event.put("flowName", "OTP_VERIFICATION");
        event.put("entityId", "test-entity");
        event.put("status", "ACTIVE");
        event.put("createdAt", LocalDateTime.now().toString());
        
        kafkaProducer.sendMessage("step-up-auth-events", stepUpAuthId, event);
        
        return stepUpAuthId;
    }

    /**
     * Create and send an OTP generation event
     */
    public void createOtpGenerationEvent(String referenceId, String stepUpAuthId, String otpCode) {
        Map<String, Object> event = new HashMap<>();
        event.put("referenceId", referenceId);
        event.put("stepUpAuthId", stepUpAuthId);
        event.put("otpCode", otpCode);
        event.put("eventType", "OTP_GENERATED");
        event.put("timestamp", LocalDateTime.now().toString());
        
        kafkaProducer.sendMessage("otp-events", referenceId, event);
    }

    /**
     * Create and send an OTP verification event
     */
    public void createOtpVerificationEvent(String referenceId, String stepUpAuthId, boolean isValid) {
        Map<String, Object> event = new HashMap<>();
        event.put("referenceId", referenceId);
        event.put("stepUpAuthId", stepUpAuthId);
        event.put("eventType", "OTP_VERIFIED");
        event.put("isValid", isValid);
        event.put("timestamp", LocalDateTime.now().toString());
        
        kafkaProducer.sendMessage("otp-events", referenceId, event);
    }

    /**
     * Create and send an OTP resend event
     */
    public void createOtpResendEvent(String originalReferenceId, String newReferenceId, String stepUpAuthId) {
        Map<String, Object> event = new HashMap<>();
        event.put("originalReferenceId", originalReferenceId);
        event.put("newReferenceId", newReferenceId);
        event.put("stepUpAuthId", stepUpAuthId);
        event.put("eventType", "OTP_RESENT");
        event.put("timestamp", LocalDateTime.now().toString());
        
        kafkaProducer.sendMessage("otp-events", newReferenceId, event);
    }

    /**
     * Create and send a fraud check event
     */
    public void createFraudCheckEvent(String profileId, String cellphone, String result) {
        Map<String, Object> event = new HashMap<>();
        event.put("profileId", profileId);
        event.put("cellphone", cellphone);
        event.put("checkType", "SIM_SWAP");
        event.put("result", result);
        event.put("timestamp", LocalDateTime.now().toString());
        
        kafkaProducer.sendMessage("fraud-events", profileId, event);
    }

    /**
     * Create and send a notification event
     */
    public void createNotificationEvent(String referenceId, String channel, String status) {
        Map<String, Object> event = new HashMap<>();
        event.put("referenceId", referenceId);
        event.put("channel", channel);
        event.put("status", status);
        event.put("eventType", "NOTIFICATION_SENT");
        event.put("timestamp", LocalDateTime.now().toString());
        
        kafkaProducer.sendMessage("notification-events", referenceId, event);
    }
}
