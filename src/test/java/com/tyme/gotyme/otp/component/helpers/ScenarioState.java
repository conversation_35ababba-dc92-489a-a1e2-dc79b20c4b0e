package com.tyme.gotyme.otp.component.helpers;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Scenario state management for sharing data between Cucumber steps
 */
@Component
public class ScenarioState {

    private final Map<String, Object> state = new HashMap<>();

    public void put(String key, Object value) {
        state.put(key, value);
    }

    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        return (T) state.get(key);
    }

    public <T> T get(String key, Class<T> type) {
        Object value = state.get(key);
        if (value != null && type.isInstance(value)) {
            return type.cast(value);
        }
        return null;
    }

    public boolean containsKey(String key) {
        return state.containsKey(key);
    }

    public void remove(String key) {
        state.remove(key);
    }

    public void clear() {
        state.clear();
    }

    // Common keys for OTP testing
    public static final String STEP_UP_AUTH_ID = "stepUpAuthId";
    public static final String REFERENCE_ID = "referenceId";
    public static final String OTP_CODE = "otpCode";
    public static final String CELLPHONE = "cellphone";
    public static final String PROFILE_ID = "profileId";
    public static final String CHANNEL = "channel";
    public static final String HTTP_RESPONSE = "httpResponse";
    public static final String HTTP_STATUS = "httpStatus";
    public static final String ERROR_MESSAGE = "errorMessage";
    public static final String GENERATED_OTP_COUNT = "generatedOtpCount";
    public static final String LAST_GENERATION_TIME = "lastGenerationTime";
}
