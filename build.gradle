buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath 'org.yaml:snakeyaml:2.2'
    }
}

plugins {
    id 'org.springframework.boot' version '3.3.3'
    id 'io.spring.dependency-management' version '1.1.5'
    id 'java'
    id 'jacoco'

    // Pact broker plugin for using pactVerify, pactPublish and canIDeploy tasks
    id 'au.com.dius.pact' version '4.2.2'
    id 'org.sonarqube' version '4.4.1.3373'
    id "io.freefair.aspectj.post-compile-weaving" version "8.2.2"
    id 'de.javaansehz.cluecumber-report-gradle-plugin' version "1.1.5"
    //Exclude plugin for compile CI
//    id 'com.tyme.hub.openapi.confluence' version '1.0.0-SNAPSHOT'
    id 'com.benjaminsproule.swagger' version '1.0.14'
}

// Comment out to avoid fail test because of checking coverage
//apply from: 'jacocoTestCoverage.gradle'
def productConfig = new org.yaml.snakeyaml.Yaml().load( new File("product-config.yaml").newInputStream() )

group = 'com.tyme.gotyme'
version = "${productConfig.Product.Version}"
//apply plugin: 'com.benjaminsproule.swagger'
sourceCompatibility = '21'
targetCompatibility = '21'

configurations {
    all*.exclude module: 'spring-boot-starter-logging'
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenLocal()
    mavenCentral {
        content {
            excludeGroupByRegex "com\\.tyme.*"
        }
    }
    maven {
        url "https://plugins.gradle.org/m2/"
        content {
            excludeGroupByRegex "com\\.tyme.*"
        }
    }
    maven {
        url "https://${System.env.CODEARTIFACT_DOMAIN}-${System.env.AWS_ACCOUNT_ID}.d.codeartifact.${System.env.AWS_REGION}.amazonaws.com/maven/libs-release/"
        credentials {
            username 'aws'
            password System.env.CODEARTIFACT_AUTH_TOKEN
        }
    }

}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    implementation "org.springdoc:springdoc-openapi-ui:${springdocVersion}"
    implementation "org.springdoc:springdoc-openapi-data-rest:${springdocVersion}"
    implementation 'io.swagger:swagger-annotations:1.6.2'

    implementation 'org.springframework.boot:spring-boot-starter-log4j2'
    runtimeOnly 'com.lmax:disruptor:3.4.2'

    implementation 'org.springframework.cloud:spring-cloud-starter-stream-kafka'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    implementation platform("io.awspring.cloud:spring-cloud-aws-dependencies:${springCloudAwsVersion}")
    implementation "io.awspring.cloud:spring-cloud-aws-starter-parameter-store"

    implementation platform('software.amazon.awssdk:bom:2.16.49')
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation "org.flywaydb:flyway-core:${flywayVersion}"
    implementation "org.flywaydb:flyway-mysql:${flywayVersion}"
    runtimeOnly 'com.mysql:mysql-connector-j'

    implementation 'commons-codec:commons-codec:1.17.0'

    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"


    implementation "org.xerial.snappy:snappy-java:${snappyJavaVersion}"
    implementation "org.aspectj:aspectjrt:1.9.21.2"
    implementation 'com.launchdarkly:launchdarkly-java-server-sdk:7.5.0'

    //Google libphonenumber
    implementation("com.googlecode.libphonenumber:libphonenumber:9.0.1")

    //JWT Extractor lib
//    implementation("com.tyme.platform:tc-mx-jwt-extractor:${jwtExtractorLib}")


    //Open feign
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-openfeign-core'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    // Cucumber BDD Testing
    testImplementation 'io.cucumber:cucumber-java:7.20.1'
    testImplementation 'io.cucumber:cucumber-spring:7.20.1'
    testImplementation 'io.cucumber:cucumber-junit:7.20.1'

    // TestContainers
    testImplementation 'org.testcontainers:junit-jupiter:1.19.8'
    testImplementation 'org.testcontainers:mysql:1.19.8'
    testImplementation 'org.testcontainers:kafka:1.19.8'
    testImplementation 'org.testcontainers:localstack:1.19.8'
    testImplementation 'org.testcontainers:testcontainers:1.19.8'

    // WireMock for Spring Boot 3.x (Jakarta EE)
    testImplementation 'org.wiremock:wiremock-standalone:3.9.1'

    // Redis TestContainer
    testImplementation 'com.redis.testcontainers:testcontainers-redis:1.6.4'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

test {
    useJUnitPlatform()
    finalizedBy jacocoTestReport
}

sonarqube {
    properties {
        property 'sonar.organization', 'tymerepos'
        property 'sonar.host.url', "https://sonarcloud.io"
        property 'sonar.qualitygate.wait', 'true'
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

bootRun {
    systemProperties = System.properties
}

jar {
    enabled = false
}

swagger {
    apiSource {
        springmvc = true
        locations = ['com.tyme.gotyme.otp.controller']
        host = 'http://localhost:8080'
        info {
            title = 'Sample API'
            version = '1.0.0'
            description = 'This is a otp project.'
        }
        swaggerDirectory = "${project.buildDir}/docs"
        tagStrategy = "class"
        outputFormats = ['yaml', 'json']
    }
}

tasks['sonarqube'].dependsOn jacocoTestReport

// Cucumber task configuration
task cucumber(type: JavaExec) {
    dependsOn assemble, testClasses
    main = "io.cucumber.core.cli.Main"
    classpath = configurations.testRuntimeClasspath + sourceSets.main.output + sourceSets.test.output
    args = [
        '--plugin', 'pretty',
        '--plugin', 'html:build/reports/cucumber',
        '--plugin', 'json:build/reports/cucumber-report.json',
        '--glue', 'com.tyme.gotyme.otp.component',
        'src/test/resources/features'
    ]
}

// Cluecumber report configuration
cluecumberReports {
    sourceJsonReportDirectory = file("build/reports")
    generatedHtmlReportDirectory = file("build/reports/cucumber-html-reports")
}
