version: 0.2

phases:
  install:
    runtime-versions:
      java: corretto21
  pre_build:
    commands:
      - aws --version
      - ls -la
  build:
    commands:
      - echo Tests started on `date`
      - mkdir cucumber-reports
      - ./gradlew cucumber
  post_build:
    commands:
      - echo Tests completed on `date`

reports:
  CucumberReports:
    base-directory: cucumber-reports
    files:
      - 'build/reports/cucumber-report.json'
    file-format: CUCUMBERJSON